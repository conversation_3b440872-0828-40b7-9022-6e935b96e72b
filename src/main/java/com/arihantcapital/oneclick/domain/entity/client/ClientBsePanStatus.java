package com.arihantcapital.oneclick.domain.entity.client;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "bse_pan_statuses")
@Getter
@Setter
public class ClientBsePanStatus implements Serializable {
    private static final ZoneId INDIA_ZONE = ZoneId.of("Asia/Kolkata");

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "identifier", unique = true, nullable = false)
    private String identifier;

    @Column(name = "client_code", unique = true, nullable = false)
    private String clientCode;

    @Column(name = "client_name")
    private String clientName;

    @Column(name = "aadhaar_seed_flag")
    private String aadhaarSeedFlag;

    @Column(name = "created_on")
    private Date createdOn;

    @Column(name = "name_on_pan_card")
    private String nameOnPanCard;

    @Column(name = "pan_name")
    private String panName;

    @Column(name = "pan_number")
    private String panNumber;

    @Column(name = "pan_validation_on")
    private String panValidationOn;

    @Column(name = "pan_status")
    private String panStatus;

    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    @PrePersist
    public void prePersist() {
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    @PreUpdate
    public void preUpdate() {
        updatedAt = Instant.now();
    }

    public void setCreatedOn(ZonedDateTime zonedDateTime) {
        this.createdOn = Date.from(zonedDateTime.toInstant());
    }

    public ZonedDateTime getCreatedOnAsZonedDateTime() {
        return createdOn.toInstant().atZone(INDIA_ZONE);
    }
}
