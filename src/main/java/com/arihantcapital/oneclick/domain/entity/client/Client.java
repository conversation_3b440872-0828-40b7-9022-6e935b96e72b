package com.arihantcapital.oneclick.domain.entity.client;

import jakarta.persistence.*;
import java.time.Instant;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "clients")
@Getter
@Setter
public class Client {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "identifier", unique = true, nullable = false)
    private String identifier;

    @Column(name = "kyc_mode")
    private String kycMode;

    @Column(name = "client_code", nullable = false)
    private String clientCode = "";

    @Column(name = "client_name")
    private String clientName = "";

    @Column(name = "pan_number")
    private String panNumber = "";

    @Column(name = "name_as_per_pan")
    private String nameAsPerPan = "";

    @Column(name = "client_type")
    private String clientType = "";

    @Column(name = "client_sub_type")
    private String clientSubType = "";

    @Column(name = "branch_code")
    private String branchCode = "";

    @Column(name = "group_code")
    private String groupCode = "";

    @Column(name = "email")
    private String email = "";

    @Column(name = "mobile")
    private String mobile = "";

    @Column(name = "dob_or_doi")
    private String dobOrDoi;

    @Column(name = "gender")
    private String gender = "";

    @Column(name = "marital_status")
    private String maritalStatus = "";

    @Column(name = "father_or_spouse_name")
    private String fatherOrSpouseName = "";

    @Column(name = "kra_code")
    private String kraCode = "";

    @Column(name = "segments")
    private List<String> segments;

    @Column(name = "client_status")
    private String clientStatus = "";

    @Column(name = "opted_for_upi")
    private String optedForUpi = "";

    @Column(name = "ddpi")
    private String ddpi = "";

    @Column(name = "ddpi_date")
    private String ddpiDate = "";

    @Column(name = "poa")
    private String poa = "";

    @Column(name = "poa_date")
    private String poaDate = "";

    @Column(name = "agreement_date")
    private String agreementDate;

    @Column(name = "ckyc_ref_no")
    private String ckycRefNo = "";

    @Column(name = "pep")
    private String pep = "";

    @Column(name = "income_range")
    private String incomeRange = "";

    @Column(name = "income_date")
    private String incomeDate;

    @Column(name = "address_proof")
    private String addressProof = "";

    @Column(name = "address_proof_ref_no")
    private String addressProofRefNo = "";

    @Column(name = "address1")
    private String address1 = "";

    @Column(name = "address2")
    private String address2 = "";

    @Column(name = "address3")
    private String address3 = "";

    @Column(name = "city")
    private String city = "";

    @Column(name = "state")
    private String state = "";

    @Column(name = "country")
    private String country = "";

    @Column(name = "pincode")
    private String pincode = "";

    @Column(name = "nationality")
    private String nationality = "";

    @Column(name = "residential_status")
    private String residentialStatus = "";

    @Column(name = "occupation")
    private String occupation = "";

    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    @PrePersist
    public void prePersist() {
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    @PreUpdate
    public void preUpdate() {
        updatedAt = Instant.now();
    }
}
