package com.arihantcapital.oneclick.domain.entity.client;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "kra_pan_statuses")
@Getter
@Setter
public class ClientKraPanStatus implements Serializable {
    private static final ZoneId INDIA_ZONE = ZoneId.of("Asia/Kolkata");

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "identifier", unique = true, nullable = false)
    private String identifier;

    @Column(name = "app_pan_no")
    private String appPanNo;

    @Column(name = "app_cor_add_proof")
    private String appCorAddProof;

    @Column(name = "app_entry_dt")
    private Date appEntryDt;

    @Column(name = "app_hold_deactive_rmks")
    private String appHoldDeactiveRmks;

    @Column(name = "app_ipv_flag")
    private String appIpvFlag;

    @Column(name = "app_kyc_mode")
    private String appKycMode;

    @Column(name = "app_mod_dt")
    private Date appModDt;

    @Column(name = "app_name")
    private String appName;

    @Column(name = "app_per_add_proof")
    private String appPerAddProof;

    @Column(name = "app_status")
    private String appStatus;

    @Column(name = "app_status_delta")
    private String appStatusDelta;

    @Column(name = "app_status_message")
    private String appStatusMessage;

    @Column(name = "app_status_dt")
    private Date appStatusDt;

    @Column(name = "app_ubo_flag")
    private String appUboFlag;

    @Column(name = "app_updt_rmks")
    private String appUpdtRmks;

    @Column(name = "app_updt_status")
    private String appUpdtStatus;

    @Column(name = "kra_code")
    private String kraCode;

    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    @PrePersist
    public void prePersist() {
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    @PreUpdate
    public void preUpdate() {
        updatedAt = Instant.now();
    }

    public void setAppEntryDt(ZonedDateTime zonedDateTime) {
        this.appEntryDt = Date.from(zonedDateTime.toInstant());
    }

    public ZonedDateTime getAppEntryDtAsZonedDateTime() {
        return appEntryDt.toInstant().atZone(INDIA_ZONE);
    }

    public void setAppModDt(ZonedDateTime zonedDateTime) {
        this.appModDt = Date.from(zonedDateTime.toInstant());
    }

    public ZonedDateTime getAppModDtAsZonedDateTime() {
        return appModDt.toInstant().atZone(INDIA_ZONE);
    }

    public void setAppStatusDt(ZonedDateTime zonedDateTime) {
        this.appStatusDt = Date.from(zonedDateTime.toInstant());
    }

    public ZonedDateTime getAppStatusDtAsZonedDateTime(ZonedDateTime zonedDateTime) {
        return appStatusDt.toInstant().atZone(INDIA_ZONE);
    }
}
