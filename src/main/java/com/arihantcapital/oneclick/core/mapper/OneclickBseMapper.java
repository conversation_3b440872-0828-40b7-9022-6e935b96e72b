package com.arihantcapital.oneclick.core.mapper;

import com.arihantcapital.oneclick.domain.entity.client.Client;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OneclickBseMapper {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static JsonNode getBseUccClientUploadRequest(Client client) {

        Map<String, Object> request = new HashMap<>();

        request.put("TRANSACTIONCODE", "New");
        request.put("BATUSER", "arihantcapital");
        request.put("CLIENTTYPE", "INDIVIDUAL");
        request.put("STATUS", "ACTIVE");
        request.put("CATEGORY", "INDIVIDUAL");
        request.put("CLIENTCODE", client.getClientCode());
        request.put("PANNO", client.getPanNumber());
        request.put("POLITICALEXPERSON", "N");
        request.put("ADDRESS1", "123, ABC Street");
        request.put("PERMNEQUALCORP", "N");
        request.put("ADDRESS2", "XYZ Colony");
        request.put("COUNTRY", "India");
        request.put("STATE", "Maharashtra");
        request.put("CITY", "Mumbai");
        request.put("PINCODE", "110001");
        request.put("TYPEOFSERVICE", "EQUITY");
        request.put("CONTACTDETAILS", "HOME");
        request.put("EMAIL", "<EMAIL>");
        request.put("MOBILENUMBER", "**********");
        request.put("STDCODE", "22");
        request.put("PHONENO", "**********");
        request.put("EQ_CPCODE", "**********");
        request.put("EQCMID", "**********");
        request.put("FNOCPCODE", "**********");
        request.put("FNOCMID", "**********");
        request.put("DEPOSITORYNAME1", "CDSL");
        request.put("DEMANTID1", "**********");
        request.put("DEPOSITORYPARTICIPANT1", "CDSL");
        request.put("DEPOSITORYNAME2", "CDSL");
        request.put("DEMANTID2", "**********");
        request.put("DEPOSITORYPARTICIPANT2", "CDSL");
        request.put("DEPOSITORYNAME3", "CDSL");
        request.put("DEMANTID3", "**********");
        request.put("DEPOSITORYPARTICIPANT3", "CDSL");
        request.put("BANKNAME1", "HDFC");
        request.put("ACCOUNTNO1", "**********");
        request.put("BANKNAME2", "HDFC");
        request.put("ACCOUNTNO2", "**********");
        request.put("BANKNAME3", "HDFC");
        request.put("ACCOUNTNO3", "**********");
        request.put("CLIENTAGGREMENTDATE", "2021-01-01");
        request.put("PROVIDEDETAILS", "N");
        request.put("INCOME", "1000000");
        request.put("INCOMEDATE", "2021-01-01");
        request.put("NETWORTH", "1000000");
        request.put("NETWORTHDATE", "2021-01-01");
        request.put("ISACTIVE", "Y");
        request.put("UPDATEREASON", "N");
        request.put("FIRSTNAME", "John");
        request.put("MIDDLENAME", "Doe");
        request.put("LASTNAME", "John");
        request.put("AADHARCARDNO", "**********12");
        request.put("DATEOFBIRTH", "1990-01-01");
        request.put("CLIENTNAME", "John Doe");
        request.put("REGISTRATIONNO", "**********");
        request.put("REGISTERINGAUTHORITY", "Mumbai");
        request.put("DATEOFREGISTRATION", "2021-01-01");
        request.put("PLACEOFREGISTRATION", "Mumbai");
        request.put("WHETHERCORPORATE", "N");
        request.put("CINNO", "**********");
        request.put("NUMBEROFDIRECTORS", "1");
        request.put("PARTNERS_KARTAUID", "**********");
        request.put("PARTNERS_COPARCENERUID", "**********");
        request.put("CONTACTPERSONNAME1", "John Doe");
        request.put("CONTACTPERSONDESIGNATION1", "Director");
        request.put("CONTACTPERSONADDRESS1", "123, ABC Street");
        request.put("CONTACTPERSONEMAIL1", "<EMAIL>");
        request.put("CONTACTPERSONPAN1", "**********");
        request.put("CONTACTPERSONMOBILE1", "**********");
        request.put("CONTACTPERSONNAME2", "Jane Doe");
        request.put("CONTACTPERSONDESIGNATION2", "Director");
        request.put("CONTACTPERSONADDRESS2", "XYZ Colony");
        request.put("CONTACTPERSONEMAIL2", "<EMAIL>");
        request.put("CONTACTPERSONPAN2", "**********");
        request.put("CONTACTPERSONMOBILE2", "**********");
        request.put("SERVERIP", "127.0.0.1");
        request.put("CASH", "Y");
        request.put("EQUITY_DERIVATIVE", "Y");
        request.put("SLB", "Y");
        request.put("CURRENCY", "Y");
        request.put("DEBT", "Y");
        request.put("ISPOA", "Y");
        request.put("POAFORFUND", "Y");
        request.put("POAFORSECURITY", "Y");
        request.put("DATEOFPOAFORFUND", "2021-01-01");
        request.put("DATEOFPOAFORSECURITY", "2021-01-01");
        request.put("PERCOUNTRY", "India");
        request.put("PERSTATE", "Maharashtra");
        request.put("PERCITY", "Mumbai");
        request.put("PERPINCODE", "110001");
        request.put("CURRENCYCPCODE", "**********");
        request.put("CURRENCYCMID", "**********");
        request.put("ENROLLMENTNUMBER", "**********");
        request.put("COMMDERIVATIVES", "Y");
        request.put("OPTEDFORNOMINATION", "Y");
        request.put("CUSTPANNO", "**********");
        request.put("CUSTNAME", "John Doe");
        request.put("CUSTADDRESS", "123, ABC Street");
        request.put("CUSTCOUNTRY", "India");
        request.put("CUSTSTATE", "Maharashtra");
        request.put("CUSTCITY", "Mumbai");
        request.put("CUSTPINCODE", "110001");
        request.put("CUSTMOBILENUMBER", "**********");
        request.put("CUSTSTDCODE", "22");
        request.put("CUSTPHONENO", "**********");
        request.put("CUSTEMAIL", "<EMAIL>");
        request.put("EGR", "N");
        request.put("BENEFICIALOWNACNTNO1", "**********");
        request.put("BENEFICIALOWNACNTNO2", "**********");
        request.put("BENEFICIALOWNACNTNO3", "**********");
        request.put("OPTED_FOR_UPI", "N");
        request.put("BANKBRANCHIFSCCODE1", "**********");
        request.put("PRIMARYORSECONDARYBANK1", "PRIMARY");
        request.put("BANKBRANCHIFSCCODE2", "**********");
        request.put("PRIMARYORSECONDARYBANK2", "SECONDARY");
        request.put("BANKBRANCHIFSCCODE3", "**********");
        request.put("PRIMARYORSECONDARYBANK3", "SECONDARY");
        request.put("BANKBRANCHIFSCCODE4", "**********");
        request.put("BANKNAME4", "HDFC");
        request.put("ACCOUNTNO4", "**********");
        request.put("PRIMARYORSECONDARYBANK4", "SECONDARY");
        request.put("BANKBRANCHIFSCCODE5", "**********");
        request.put("BANKNAME5", "HDFC");
        request.put("ACCOUNTNO5", "**********");
        request.put("PRIMARYORSECONDARYBANK5", "SECONDARY");
        request.put("PRIMARYORSECONDARYDP1", "PRIMARY");
        request.put("PRIMARYORSECONDARYDP2", "SECONDARY");
        request.put("PRIMARYORSECONDARYDP3", "SECONDARY");
        request.put("DEPOSITORYNAME4", "CDSL");
        request.put("DEMANTID4", "**********");
        request.put("BENEFICIALOWNACNTNO4", "**********");
        request.put("PRIMARYORSECONDARYDP4", "SECONDARY");
        request.put("DEPOSITORYNAME5", "CDSL");
        request.put("DEMANTID5", "**********");
        request.put("BENEFICIALOWNACNTNO5", "**********");
        request.put("PRIMARYORSECONDARYDP5", "SECONDARY");
        request.put("CLIENTNAMEDESCRIPTION", "John Doe");

        // Director Details
        Map<String, String> directorDetails = new HashMap<>();
        directorDetails.put("OPERATIONFLAG", "N");
        directorDetails.put("CLIENTCODE", client.getClientCode());
        directorDetails.put("NAMEOFDIRECTOR", client.getClientName());
        directorDetails.put("DIN", "**********");
        directorDetails.put("WHETHERFOREIGNRESIDENT", "N");
        directorDetails.put("PANNO", client.getPanNumber());
        directorDetails.put("DIRDESIGNATION", "Director");
        directorDetails.put("DIRALLOWTOTRADE", "Y");
        directorDetails.put("DIRADDRESS", "123, ABC Street");
        directorDetails.put("DIREMAILID", "<EMAIL>");
        directorDetails.put("DIRAADHARCARDNO", "**********12");
        directorDetails.put("DIRENROLLMENTNO", "**********");
        directorDetails.put("DIRMOBILENO", "**********");
        request.put("DIRECTORDETAILS", List.of(directorDetails));

        return objectMapper.valueToTree(request);
    }

    public static void main(String[] args) {
        System.out.println("OneclickBseMapper!");
    }
}
