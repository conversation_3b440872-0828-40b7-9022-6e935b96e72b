package com.arihantcapital.oneclick.core.mapper;

import com.arihantcapital.oneclick.domain.entity.client.Client;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OneclickNseMapper {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static JsonNode getNseClientUploadRequest(Client client) {

        Map<String, String> request = new HashMap<>();
        request.put("ccdMemCd", "07839");
        request.put("ccdCd", client.getClientCode());
        request.put("ccdName", client.getClientName());
        request.put("ccdCategory", "INDIVIDUAL");
        request.put("ccdPinCode", "110001");
        request.put("ccdTelNo", "**********");
        request.put("ccdAgrDt", "2021-01-01");
        request.put("ccdPanNo", client.getPanNumber());
        request.put("ccdBankName", "HDFC");
        request.put("ccdBankAcctNo", "**********");
        request.put("ccdDeposName", "CDSL");
        request.put("ccdBenAcctNo", "**********");
        request.put("ccdRegNo", "**********");
        request.put("ccdRegAuth", "CDSL");
        request.put("ccdPlcReg", "Mumbai");
        request.put("ccdDtReg", "2021-01-01");
        request.put("ccdSegInd", "EQUITY");
        request.put("ccdDob", "1990-01-01");
        request.put("ccdDeposId", "**********");
        request.put("ccdAddLine1", "123, ABC Street");
        request.put("ccdAddLine2", "XYZ Colony");
        request.put("ccdAddLine3", "Mumbai");
        request.put("ccdAddCity", "Mumbai");
        request.put("ccdAddState", "Maharashtra");
        request.put("ccdAddCountry", "India");
        request.put("ccdTelIsd", "91");
        request.put("ccdTelStd", "22");
        request.put("ccdMobile", "**********");
        request.put("ccdEmail", "<EMAIL>");
        request.put("ccdProofType", "PAN");
        request.put("ccdProofNo", client.getPanNumber());
        request.put("ccdIssPlcProof", "Mumbai");
        request.put("ccdIssDtProof", "2021-01-01");
        request.put("ccdDir1Name", "John Doe");
        request.put("ccdDir2Name", "Jane Doe");
        request.put("ccdDir3Name", "John Doe");
        request.put("ccdDir1Desig", "Director");
        request.put("ccdDir2Desig", "Director");
        request.put("ccdDir3Desig", "Director");
        request.put("ccdDir1Pan", "**********");
        request.put("ccdDir2Pan", "**********");
        request.put("ccdDir3Pan", "**********");
        request.put("ccdDir1Add", "123, ABC Street");
        request.put("ccdDir2Add", "XYZ Colony");
        request.put("ccdDir3Add", "Mumbai");
        request.put("ccdDir1TelNo", "**********");
        request.put("ccdDir2TelNo", "**********");
        request.put("ccdDir3TelNo", "**********");
        request.put("ccdDir1Email", "<EMAIL>");
        request.put("ccdDir2Email", "<EMAIL>");
        request.put("ccdDir3Email", "<EMAIL>");
        request.put("ccdIpv", "N");
        request.put("ccdUccCd", "**********");
        request.put("ccdRelationship", "SELF");
        request.put("ccdTvFlag", "N");
        request.put("ccdFacilityType", "N");
        request.put("ccdCin", "**********");
        request.put("ccdCltStatus", "ACTIVE");
        request.put("ccdCltStatusReason", "N");
        request.put("ccdGender", "MALE");
        request.put("ccdGuardianName", "John Doe");
        request.put("ccdMaritlStatus", "SINGLE");
        request.put("ccdNationality", "INDIAN");
        request.put("ccdPermantAddFlg", "N");
        request.put("ccdPermAddLine1", "123, ABC Street");
        request.put("ccdPermAddLine2", "XYZ Colony");
        request.put("ccdPermAddLine3", "Mumbai");
        request.put("ccdPermAddCity", "Mumbai");
        request.put("ccdPermAddState", "Maharashtra");
        request.put("ccdPermAddCountry", "India");
        request.put("ccdPermPin", "110001");
        request.put("ccdOffcIsd", "91");
        request.put("ccdOffcStd", "22");
        request.put("ccdTelOffice", "**********");
        request.put("ccdUid", "**********");
        request.put("ccdBankName2", "HDFC");
        request.put("ccdBankAcctNo2", "**********");
        request.put("ccdBankName3", "HDFC");
        request.put("ccdBankAcctNo3", "**********");
        request.put("ccdDeposName2", "CDSL");
        request.put("ccdDeposId2", "**********");
        request.put("ccdBenAcctNo2", "**********");
        request.put("ccdDeposName3", "CDSL");
        request.put("ccdDeposId3", "**********");
        request.put("ccdBenAcctNo3", "**********");
        request.put("ccdGrosAnnlRnge", "1000000");
        request.put("ccdGrosAnnlAsdate", "2021-01-01");
        request.put("ccdNetWorth", "1000000");
        request.put("ccdNetWorthAsdate", "2021-01-01");
        request.put("ccdPep", "N");
        request.put("ccdPoi", "N");
        request.put("ccdOccupation", "EMPLOYED");
        request.put("ccdOccupationDtls", "Software Engineer");
        request.put("ccdBusComDt", "2021-01-01");
        request.put("ccdCpCd", "**********");
        request.put("ccdClntTyp", "INDIVIDUAL");
        request.put("ccdDinCntrctPerson1", "**********");
        request.put("ccdUidCntrctPerson1", "**********");
        request.put("ccdDinCntrctPerson2", "**********");
        request.put("ccdUidCntrctPerson2", "**********");
        request.put("ccdDinCntrctPerson3", "**********");
        request.put("ccdUidCntrctPerson3", "**********");
        request.put("ccdDir4Name", "John Doe");
        request.put("ccdDir4Desig", "Director");
        request.put("ccdDir4Pan", "**********");
        request.put("ccdDir4Add", "123, ABC Street");
        request.put("ccdDir4TelNo", "**********");
        request.put("ccdDinCntrctPerson4", "**********");
        request.put("ccdUidCntrctPerson4", "**********");
        request.put("ccdDir4Email", "<EMAIL>");
        request.put("ccdDir5Name", "John Doe");
        request.put("ccdDir5Desig", "Director");
        request.put("ccdDir5Pan", "**********");
        request.put("ccdDir5Add", "123, ABC Street");
        request.put("ccdDir5TelNo", "**********");
        request.put("ccdDinCntrctPerson5", "**********");
        request.put("ccdUidCntrctPerson5", "**********");
        request.put("ccdDir5Email", "<EMAIL>");
        request.put("ccdNationalityOth", "N");
        request.put("ccdPermAddStateOth", "N");
        request.put("ccdAddStateOth", "N");
        request.put("ccdPoaFunds", "N");
        request.put("ccdPoaSecurities", "N");
        request.put("ccdCpName", "John Doe");
        request.put("ccdCpPan", "**********");
        request.put("ccdCpEmail", "<EMAIL>");
        request.put("ccdCpStd", "22");
        request.put("ccdCpIsd", "91");
        request.put("ccdCpTel", "**********");
        request.put("ccdCpMob", "**********");
        request.put("ccdCpAdd", "123, ABC Street");
        request.put("ccdCpAddCity", "Mumbai");
        request.put("ccdCpAddState", "Maharashtra");
        request.put("ccdCpAddStateOth", "N");
        request.put("ccdCpAddCountry", "India");
        request.put("ccdCpAddPin", "110001");
        request.put("ccdNomFlag", "N");
        request.put("ccdBankIfsc", "HDFC0000001");
        request.put("ccdBankIfsc2", "HDFC0000002");
        request.put("ccdBankIfsc3", "HDFC0000003");
        request.put("ccdBankIfsc4", "HDFC0000004");
        request.put("ccdBankIfsc5", "HDFC0000005");
        request.put("ccdPriSecBnk", "PRIMARY");
        request.put("ccdPriSecBnk2", "SECONDARY");
        request.put("ccdPriSecBnk3", "SECONDARY");
        request.put("ccdPriSecBnk4", "SECONDARY");
        request.put("ccdPriSecBnk5", "SECONDARY");
        request.put("ccdPriSecDp", "PRIMARY");
        request.put("ccdPriSecDp2", "SECONDARY");
        request.put("ccdPriSecDp3", "SECONDARY");
        request.put("ccdPriSecDp4", "SECONDARY");
        request.put("ccdPriSecDp5", "SECONDARY");
        request.put("ccdAcctType", "SAVINGS");
        request.put("ccdOptForUpi", "N");
        request.put("ccdBankName4", "HDFC");
        request.put("ccdBankAcctNo4", "**********");
        request.put("ccdBankName5", "HDFC");
        request.put("ccdBankAcctNo5", "**********");
        request.put("ccdDeposName4", "CDSL");
        request.put("ccdDeposId4", "**********");
        request.put("ccdBenAcctNo4", "**********");
        request.put("ccdDeposName5", "CDSL");
        request.put("ccdDeposId5", "**********");
        request.put("ccdBenAcctNo5", "**********");

        return objectMapper.createObjectNode();
    }

    public static JsonNode getNseClientUploadRequest(List<Client> clients) {
        List<JsonNode> request = new ArrayList<>();
        clients.forEach(client -> {
            request.add(getNseClientUploadRequest(client));
        });
        return objectMapper.valueToTree(request);
    }

    public static void main(String[] args) {
        System.out.println("OneclickNseMapper!");
    }
}
