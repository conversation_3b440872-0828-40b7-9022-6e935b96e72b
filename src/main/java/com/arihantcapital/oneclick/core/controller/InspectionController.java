package com.arihantcapital.oneclick.core.controller;

import com.arihantcapital.oneclick.core.service.InspectionService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/inspection")
@RequiredArgsConstructor
public class InspectionController {

    private final InspectionService inspectionService;

    @PostMapping(value = "/get-korp-accounts-opened-in-time-range")
    public ResponseEntity<JsonNode> getKorpAccountsOpenedInTimeRange(@RequestBody JsonNode payload) {
        return ResponseEntity.ok(inspectionService.getKorpAccountsOpenedInTimeRange(
                payload.get("fromDate").asText(),
                payload.get("toDate").asText(),
                payload.get("size").asText(),
                payload.get("pageNumber").asText()));
    }
}
