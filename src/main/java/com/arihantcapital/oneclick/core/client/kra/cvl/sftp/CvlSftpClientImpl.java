package com.arihantcapital.oneclick.core.client.kra.cvl.sftp;

import com.arihantcapital.oneclick.OneclickProperties;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.apache.sshd.client.SshClient;
import org.apache.sshd.client.session.ClientSession;
import org.apache.sshd.sftp.client.SftpClient;
import org.apache.sshd.sftp.client.SftpClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.Duration;

public class CvlSftpClientImpl {
    private static final Logger log = LoggerFactory.getLogger(CvlSftpClientImpl.class);

    private CvlSftpClientImpl.ClientConfig clientConfig = new ClientConfig();

    private SshClient sshClient;
    private ClientSession session;
    private SftpClient sftpClient;

    private static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(30);
    private static final Duration DEFAULT_AUTH_TIMEOUT = Duration.ofSeconds(10);

    public CvlSftpClientImpl(CvlSftpClientImpl.ClientConfig clientConfig) {
        this.clientConfig = clientConfig;
        log.info("Cvl Sftp Client Config: {}", clientConfig);
    }

    public CvlSftpClientImpl() {
    }

    /**
     * Connect using username and password
     */
    public void connect() throws IOException {
        String host = clientConfig.getHost();
        int port = Integer.parseInt(clientConfig.getPort());
        String username = clientConfig.getUsername();
        String password = clientConfig.getPassword();

        // Initialze SSH Client
        sshClient = SshClient.setUpDefaultClient();
        sshClient.start();

        // Create Session
        session =
                sshClient.connect(username, host, port).verify(DEFAULT_TIMEOUT).getSession();
        session.addPasswordIdentity(password);

        // Authentication
        boolean authenticated = session.auth().verify(DEFAULT_AUTH_TIMEOUT).isSuccess();
        if (authenticated) {
            log.info("✅ Successfully Authenticated to CVl KRA SFTP Server using username and password.");
        }

        // Create SFTP Client
        SftpClientFactory factory = SftpClientFactory.instance();

        sftpClient = factory.createSftpClient(session);
        log.info("✅ Cvl Sftp connection established successfully to {}:{}", host, port);
    }


    public void disconnect() {
        try {
            sftpClient.close();
            session.close();
            sshClient.stop();
        } catch (Exception e) {
            log.error("Error disconnecting from CVL KRA SFTP Server: {}", e.getMessage());
        }
    }

    public void uploadFile(String localFilePath, String remoteFilePath) {
        try {

            log.info("✅ File uploaded successfully to {}", remoteFilePath);
        } catch (Exception e) {
            log.error("Error uploading file to CVL KRA SFTP Server: {}", e.getMessage());
        }
    }

    /**
     * Download a file from the SFTP server to local file system
     *
     * @param remoteFilePath Path of the file on the SFTP server
     * @param localFilePath  Local path where the file should be saved
     * @return true if download was successful, false otherwise
     */
    public boolean downloadFile(String remoteFilePath, String localFilePath) {
        try {
            if (sftpClient == null) {
                log.error("⚠️ SFTP client is not connected. Please connect first.");
                return false;
            }

            log.info("Starting download from remote path: {} to local path: {}", remoteFilePath, localFilePath);

            // Create local directory if it doesn't exist
            Path localPath = Paths.get(localFilePath);
            Path parentDir = localPath.getParent();
            if (parentDir != null && !Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
                log.info("Created directory: {}", parentDir);
            }

            // Download file using SFTP client
            try (InputStream inputStream = sftpClient.read(remoteFilePath)) {
                Files.copy(inputStream, localPath, StandardCopyOption.REPLACE_EXISTING);
            }

            log.info("✅ File downloaded successfully from {} to {}", remoteFilePath, localFilePath);
            return true;

        } catch (Exception e) {
            log.error("❌ Error downloading file from CVL KRA SFTP Server: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Download a file from the SFTP server and return as byte array
     *
     * @param remoteFilePath Path of the file on the SFTP server
     * @return byte array of the file content, or null if download failed
     */
    public byte[] downloadFile(String remoteFilePath) {
        try {
            if (sftpClient == null) {
                log.error("⚠️ SFTP client is not connected. Please connect first.");
                return null;
            }

            log.info("Starting download from remote path: {} as byte array", remoteFilePath);

            // Download file using SFTP client and read into byte array
            try (InputStream inputStream = sftpClient.read(remoteFilePath);
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                byte[] fileBytes = outputStream.toByteArray();
                log.info("✅ File downloaded successfully from {} as byte array (size: {} bytes)",
                        remoteFilePath, fileBytes.length);
                return fileBytes;
            }

        } catch (Exception e) {
            log.error("❌ Error downloading file from CVL KRA SFTP Server: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Check if a file exists on the SFTP server
     *
     * @param remoteFilePath Path of the file on the SFTP server
     * @return true if file exists, false otherwise
     */
    public boolean fileExists(String remoteFilePath) {
        try {
            if (sftpClient == null) {
                log.error("⚠️ SFTP client is not connected. Please connect first.");
                return false;
            }

            log.info("Checking if file exists at remote path: {}", remoteFilePath);

            // Use stat() to check if file exists - this will throw an exception if file doesn't exist
            sftpClient.stat(remoteFilePath);

            log.info("✅ File exists at remote path: {}", remoteFilePath);
            return true;

        } catch (Exception e) {
            log.info("❌ File does not exist at remote path: {} - {}", remoteFilePath, e.getMessage());
            return false;
        }
    }

    @Data
    public static class ClientConfig {
        @NotBlank(message = "Host is required")
        private String host = "**************";

        @NotBlank(message = "Port is required")
        private String port = "22";

        @NotBlank(message = "Username is required")
        private String username = "arihant";

        @NotBlank(message = "Password is required")
        private String password = "arihant@123";

        public ClientConfig(String host, String port, String username, String password) {
            this.host = host;
            this.port = port;
            this.username = username;
            this.password = password;
        }

        public ClientConfig(OneclickProperties.KraConfig.CvlConfig.SftpConfig config) {
            this.host = config.host();
            this.port = config.port();
            this.username = config.username();
            this.password = config.password();
        }

        public ClientConfig() {
        }
    }
}
