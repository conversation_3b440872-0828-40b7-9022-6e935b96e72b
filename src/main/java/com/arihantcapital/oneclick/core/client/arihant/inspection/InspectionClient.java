package com.arihantcapital.oneclick.core.client.arihant.inspection;

import com.arihantcapital.oneclick.OneclickProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
@Service
public class InspectionClient {

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    private InspectionClient.ClientConfig clientConfig = new ClientConfig();

    public InspectionClient(InspectionClient.ClientConfig clientConfig) {
        this.clientConfig = clientConfig;
        log.info("Arihant Inspection Client Config: {}", clientConfig);
    }

    public InspectionClient() {
    }

    public JsonNode getKorpAccountsOpenedInTimeRange(String fromDate, String toDate, String size, String pageNumber) {

        String url = UriComponentsBuilder.fromUriString(clientConfig.getBaseUrl() + ClientRoutes.GetKorpAccountsOpenedInTimeRange)
                .queryParam("FromDate", fromDate)
                .queryParam("ToDate", toDate)
                .queryParam("size", size)
                .queryParam("pageNumber", pageNumber)
                .toUriString();
        log.info("Initiating Get KORP Accounts Opened In Time Range (RestTemplate) for fromDate: {}, toDate: {}, URl: {}", fromDate, toDate, url);

        // HTTP Request Headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer " + clientConfig.getToken());

        // HTTP Request Body
        HttpEntity<String> requestEntity = new HttpEntity<>(headers);

        // HTTP Request Send
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("Get KORP Accounts Opened In Time Range Response: {}", responseString);

        return responseString;
    }

    @Data
    public static class ClientConfig {

        @NotBlank(message = "Base URL is required")
        private String baseUrl = "https://inspection.arihantcapital.com";

        @NotBlank(message = "Token is required")
        private String token = "";

        public ClientConfig(String baseUrl, String token) {
            this.baseUrl = baseUrl;
            this.token = token;
        }

        public ClientConfig(OneclickProperties.InspectionConfig config) {
            this.baseUrl = config.baseUrl();
            this.token = config.token();
        }

        public ClientConfig() {
        }
    }

    // Client Routes
    public static class ClientRoutes {
        public static final String GetKorpAccountsOpenedInTimeRange = "/api/v1/CtC/korpGetAccountOpenDateWise";
    }


}
