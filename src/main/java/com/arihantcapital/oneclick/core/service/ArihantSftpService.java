package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.client.arihant.sftp.ArihantSftpClientImpl;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ArihantSftpService {

    private final OneclickProperties.ArihantSftpConfig config;
    private final ArihantSftpClientImpl arihantSftpClient;

    public ArihantSftpService(OneclickProperties.ArihantSftpConfig config) {
        this.config = config;
        ArihantSftpClientImpl.ClientConfig clientConfig = new ArihantSftpClientImpl.ClientConfig(config);
        this.arihantSftpClient = new ArihantSftpClientImpl(clientConfig);
        log.info("Arihant SFTP Service initialized with client config: {}", clientConfig);
    }

    public void connect() {
        try {
            arihantSftpClient.connect();
        } catch (Exception e) {
            log.error("Error connecting to Arihant SFTP Server: {}", e.getMessage());
        }
    }

    public void disconnect() {
        try {
            arihantSftpClient.disconnect();
        } catch (Exception e) {
            log.error("Error disconnecting from Arihant SFTP Server: {}", e.getMessage());
        }
    }

    /**
     * Upload a file from local file system to the Arihant SFTP server
     * @param localFilePath Path of the local file to upload
     * @param remoteFilePath Path where the file should be saved on the SFTP server
     * @return true if upload was successful, false otherwise
     */
    public boolean uploadFile(String localFilePath, String remoteFilePath) {
        return arihantSftpClient.uploadFile(localFilePath, remoteFilePath);
    }

    /**
     * Upload a file from byte array to the Arihant SFTP server
     * @param fileBytes Byte array containing the file content
     * @param remoteFilePath Path where the file should be saved on the SFTP server
     * @return true if upload was successful, false otherwise
     */
    public boolean uploadFile(byte[] fileBytes, String remoteFilePath) {
        return arihantSftpClient.uploadFile(fileBytes, remoteFilePath);
    }
}
