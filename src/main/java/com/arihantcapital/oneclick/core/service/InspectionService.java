package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.client.arihant.inspection.InspectionClient;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class InspectionService {

    InspectionClient inspectionClient;

    public InspectionService(OneclickProperties.InspectionConfig config) {
        InspectionClient.ClientConfig clientConfig = new InspectionClient.ClientConfig(config);
        inspectionClient = new InspectionClient(clientConfig);
        log.info("Inspection Service initialized with client config: {}", clientConfig);
    }

    public JsonNode getKorpAccountsOpenedInTimeRange(String fromDate, String toDate, String size, String pageNumber) {
        try {
            return inspectionClient.getKorpAccountsOpenedInTimeRange(fromDate, toDate, size, pageNumber);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }
}
