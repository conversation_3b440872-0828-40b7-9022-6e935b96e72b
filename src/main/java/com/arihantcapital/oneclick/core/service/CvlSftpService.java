package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.client.kra.cvl.sftp.CvlSftpClientImpl;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CvlSftpService {

    private final OneclickProperties.KraConfig.CvlConfig.SftpConfig config;
    private CvlSftpClientImpl cvlSftpClient;

    public CvlSftpService(OneclickProperties.KraConfig.CvlConfig.SftpConfig config) {
        this.config = config;
        CvlSftpClientImpl.ClientConfig clientConfig = new CvlSftpClientImpl.ClientConfig(config);
        this.cvlSftpClient = new CvlSftpClientImpl(clientConfig);
        log.info("CVLKRA SFTP Service initialized with client config: {}", clientConfig);
    }

    public void connect() {
        try {
            cvlSftpClient.connect();
        } catch (Exception e) {
            log.error("Error connecting to CVL KRA SFTP Server: {}", e.getMessage());
        }
    }

    public void disconnect() {
        try {
            cvlSftpClient.disconnect();
        } catch (Exception e) {
            log.error("Error disconnecting from CVL KRA SFTP Server: {}", e.getMessage());
        }
    }

    /**
     * Download a file from the CVL KRA SFTP server
     *
     * @param remoteFilePath Path of the file on the SFTP server
     * @param localFilePath  Local path where the file should be saved
     * @return true if download was successful, false otherwise
     */
    public boolean downloadFile(String remoteFilePath, String localFilePath) {
        return cvlSftpClient.downloadFile(remoteFilePath, localFilePath);
    }

    /**
     * Download a file from the CVL KRA SFTP server and return as byte array
     *
     * @param remoteFilePath Path of the file on the SFTP server
     * @return byte array of the file content, or null if download failed
     */
    public byte[] downloadFile(String remoteFilePath) {
        return cvlSftpClient.downloadFile(remoteFilePath);
    }

    /**
     * Check if a file exists on the CVL KRA SFTP server
     *
     * @param remoteFilePath Path of the file on the SFTP server
     * @return true if file exists, false otherwise
     */
    public boolean fileExists(String remoteFilePath) {
        return cvlSftpClient.fileExists(remoteFilePath);
    }

    /**
     * Upload a file from local file system to the CVL KRA SFTP server
     * @param localFilePath Path of the local file to upload
     * @param remoteFilePath Path where the file should be saved on the SFTP server
     * @return true if upload was successful, false otherwise
     */
    public boolean uploadFile(String localFilePath, String remoteFilePath) {
        return cvlSftpClient.uploadFile(localFilePath, remoteFilePath);
    }

    /**
     * Upload a file from byte array to the CVL KRA SFTP server
     * @param fileBytes Byte array containing the file content
     * @param remoteFilePath Path where the file should be saved on the SFTP server
     * @return true if upload was successful, false otherwise
     */
    public boolean uploadFile(byte[] fileBytes, String remoteFilePath) {
        return cvlSftpClient.uploadFile(fileBytes, remoteFilePath);
    }
}
