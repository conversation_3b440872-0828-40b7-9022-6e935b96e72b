package com.arihantcapital.oneclick.ws.controller;

import com.arihantcapital.oneclick.service.KycService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/request")
@RequiredArgsConstructor
public class RequestController {

    private final KycService kycService;

    @PostMapping(value = "/kyc")
    public ResponseEntity<JsonNode> requestKyc(@RequestBody JsonNode body) {
        return ResponseEntity.ok(kycService.processKycRequest(body));
    }

    @PostMapping(value = "/re-kyc")
    public ResponseEntity<JsonNode> requestReKyc(@RequestBody JsonNode body) {
        return ResponseEntity.ok(kycService.processKycRequest(body));
    }
}
