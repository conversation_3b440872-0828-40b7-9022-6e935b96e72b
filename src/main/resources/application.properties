spring.application.name=oneclick
# Timezone Configuration
spring.jackson.time-zone=Asia/Kolkata
spring.jackson.serialization.write-dates-as-timestamps=false

spring.jpa.hibernate.ddl-auto=validate
spring.flyway.enabled=true

# Optional: Set JVM timezone (affects entire application)
user.timezone=Asia/Kolkata
logging.level.root=INFO

######## Arihant Sftp Configuration  #########
oneclick.arihant-sftp.enabled=true
oneclick.arihant-sftp.host=**********
oneclick.arihant-sftp.port=22
oneclick.arihant-sftp.username=arihant
oneclick.arihant-sftp.password=arihant@123


######## S3 Configuration  #########
oneclick.s3.enabled=true
oneclick.s3.health-check=true
oneclick.s3.access-key=${S3_ACCESS_KEY:test}
oneclick.s3.secret-key=${S3_SECRET_KEY:test}
oneclick.s3.region=${S3_REGION:ap-south-1}
oneclick.s3.bucket-name=${S3_BUCKET_NAME:arihant-oneclick}


######## Postgres Database Configuration  #########
spring.datasource.url=${DB_URL:*****************************************}
spring.datasource.username=${DB_USERNAME:postgres}
spring.datasource.password=${DB_PASSWORD:postgres}
spring.jpa.open-in-view=false
spring.jpa.show-sql=true

######## Email Configuration  #########
email.default-from-name=Arihant Capital
email.default-from-address=<EMAIL>
email.retry-attempts=3
email.retry-delay-ms=1000

# SendGrid Provider
email.providers[0].name=sendgrid
email.providers[0].host=smtp.sendgrid.net
email.providers[0].port=587
email.providers[0].username=${SENDGRID_USERNAME}
email.providers[0].password=${SENDGRID_API_KEY}
email.providers[0].priority=1
email.providers[0].enabled=true
email.providers[0].properties.mail.smtp.auth=true
email.providers[0].properties.mail.smtp.starttls.enable=false

# SendClean Provider
email.providers[1].name=sendclean
email.providers[1].host=trans-mta.sendclean.net
email.providers[1].port=587
email.providers[1].username=${SENDCLEAN_USERNAME}
email.providers[1].password=${SENDCLEAN_API_KEY}
email.providers[1].priority=2
email.providers[1].enabled=true
email.providers[1].properties.mail.smtp.auth=true
email.providers[1].properties.mail.smtp.starttls.enable=false

# ACL Emails Provider
email.providers[2].name=aclemails
email.providers[2].host=smtp.aclemails.com
email.providers[2].port=587
email.providers[2].username=${ACLEMAILS_USERNAME}
email.providers[2].password=${ACLEMAILS_API_KEY}
email.providers[2].priority=3
email.providers[2].enabled=true
email.providers[2].properties.mail.smtp.auth=true
email.providers[2].properties.mail.smtp.starttls.enable=false


######## KORP Configuration  #########
oneclick.korp.enabled=true
oneclick.korp.base-url=http://uatbo.arihantcapital.com:15000
oneclick.korp.username=KYC
oneclick.korp.password=Orion@1234
oneclick.korp.grant-type=password


######## Inspection Configuration  #########
oneclick.inspection.enabled=true
oneclick.inspection.base-url=https://inspection.arihantcapital.com
oneclick.inspection.token=c2FtcGFyay5hcmloYW50Y2FwaXRhbDpBcmloYW50QDEyMzQ1


######## MSIL Configuration  #########
oneclick.msil.enabled=true
oneclick.msil.base-url=https://cug-ws.arihantcapital.com
oneclick.msil.token=YWNtbDo0MzIxQEFyaWhhbnQ=



######## NSE UCC Configuration  #########
oneclick.nse-ucc.enabled=true
oneclick.nse-ucc.base-url=https://ims.connect2nsccl.com
oneclick.nse-ucc.username=07839
oneclick.nse-ucc.password=Acml07839@34
oneclick.nse-ucc.api-name=uci_clientupload



######## BSE UCC Configuration  #########
oneclick.bse-ucc.enabled=true
oneclick.bse-ucc.api-version=V5
oneclick.bse-ucc.base-url=https://ucc.bseindia.com
oneclick.bse-ucc.member-code=0313
oneclick.bse-ucc.username=0313.0313D
oneclick.bse-ucc.password=Bse@54321
oneclick.bse-ucc.public-key-xml=<RSAKeyValue><Modulus>xWlQk3gh0ImHOXhnhtbWZZ3FqtHI+v+5CL5Hs8mR/nS/Q/ewNPMWJ6n0RY6rQysH0NP3AoAujI3P2p7VC9B2e7QyVcDucLw5GiqWJuxaJ76OoeHL5shzglFZrWkDLrccHGNUaH04co8G3RNM3lu04XU2CMQt0awuUsDvkCkJyeVV+ouyVTDF0mP7a1bGSQj9tRnl0cNh4PElpAd4vAnn+TAgMJBJQ/hLqcx+IUH0XZfVBpfp0THOIuw8E83FQvukJWKFrpAxoTXczSQE7Zqsmjg3GAQ0UMsoikDbGL1Vhe7CHmuj9QVGbUwN3NBhDcGpe3JmNT5QWWzsqYMZD9O08w==</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>

######## BSE Star Mutual Fund Configuration  #########
oneclick.bse-star-mutual-fund.enabled=true
oneclick.bse-star-mutual-fund.base-url=https://www.bsestarmf.in
oneclick.bse-star-mutual-fund.member-code=0313
oneclick.bse-star-mutual-fund.username=031306
oneclick.bse-star-mutual-fund.password=Arihant@280525


######## CKYC Configuration  #########
oneclick.ckyc.enabled=true
oneclick.ckyc.api-version=1.3
oneclick.ckyc.fi-code=IN0888
oneclick.ckyc.base-url=https://www.ckycindia.in
oneclick.ckyc.key-store-type=PKCS12
oneclick.ckyc.cersai-public-key-file-path=resources/ckyc_CersaiSignPublicKey.cer
oneclick.ckyc.fi-certificate-key-store-file-path=resources/ckyc_rohit.pfx
oneclick.ckyc.fi-certificate-key-store-file-password=roh123
oneclick.ckyc.fi-certificate-private-key-alias=te-9d800711-1c56-4a7e-8c77-d3c22ffe5da0

######## CVLKRA OTP CLIENT CONFIGURATION  #########
oneclick.kra.cvl.otp.enabled=true
oneclick.kra.cvl.otp.base-url=abc
oneclick.kra.cvl.otp.username=abc
oneclick.kra.cvl.otp.password=abc

######## CVLKRA PANCHECK XML CLIENT CONFIGURATION  #########
oneclick.kra.cvl.pancheck.xml.enabled=true
oneclick.kra.cvl.pancheck.xml.base-url=https://pancheck.www.kracvl.com
oneclick.kra.cvl.pancheck.xml.username=NEWEKYC
oneclick.kra.cvl.pancheck.xml.password=acml@1234
oneclick.kra.cvl.pancheck.xml.pos-code=1100043000
oneclick.kra.cvl.pancheck.xml.rta-code=1100043000

######## CVLKRA PANCHECK JSON CLIENT CONFIGURATION  #########
oneclick.kra.cvl.pancheck.json.enabled=true
oneclick.kra.cvl.pancheck.json.user-agent=oneclick
oneclick.kra.cvl.pancheck.json.base-url=https://api.kracvl.com
oneclick.kra.cvl.pancheck.json.api-key=1027d73fe55441ef95c96c0cd21601c0
oneclick.kra.cvl.pancheck.json.aes-key=4c8c98585cfb425bb8ee3a003d535c8c
oneclick.kra.cvl.pancheck.json.username=EKYC
oneclick.kra.cvl.pancheck.json.password=Ekyc@1234567
oneclick.kra.cvl.pancheck.json.pos-code=1100043000
oneclick.kra.cvl.pancheck.json.rta-code=1100043000
oneclick.kra.cvl.pancheck.json.token-valid-time=3600

######## CLKRA SFTP CONFIGURATION  #########
oneclick.kra.cvl.sftp.enabled=true
oneclick.kra.cvl.sftp.host=dm.cvlindia.com
oneclick.kra.cvl.sftp.port=4443
oneclick.kra.cvl.sftp.username=1100043000_D
oneclick.kra.cvl.sftp.password=Jan_2018


######## ODIN UUM CLIENT CONFIGURATION  #########
oneclick.odin-uum.enabled=true
oneclick.odin-uum.base-url=http://**************:9898/usercreationwcfservice/UserCreation.svc/soap
oneclick.odin-uum.username=RESERVEDP4
oneclick.odin-uum.password=ftodin1
oneclick.odin-uum.ip-address=127.0.0.1


######## Actuator Configuration  #########
management.info.git.mode=full
management.endpoints.web.exposure.include=*
management.metrics.tags.application=${spring.application.name}
management.tracing.enabled=false
management.tracing.sampling.probability=1.0


